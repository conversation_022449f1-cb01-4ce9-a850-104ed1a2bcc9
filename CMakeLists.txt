CMAKE_MINIMUM_REQUIRED(VERSION 3.10)

SET(CMAKE_SYSTEM_NAME Generic)
SET(CMAKE_SYSTEM_PROCESSOR cortex-m4)
#SET(CMAKE_VERBOSE_MAKEFILE ON)

SET(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(CMAKE_C_COMPILER "C:/env-windows/tools/bin/../../tools/gnu_gcc/arm_gcc/mingw/bin/arm-none-eabi-gcc.exe")
SET(CMAKE_ASM_COMPILER "C:/env-windows/tools/bin/../../tools/gnu_gcc/arm_gcc/mingw/bin/arm-none-eabi-gcc.exe")
SET(CMAKE_C_FLAGS " -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -ffunction-sections -fdata-sections -Dgcc -Os")
SET(CMAKE_ASM_FLAGS " -c -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -ffunction-sections -fdata-sections -x assembler-with-cpp -Wa,-mimplicit-it=thumb  -gdwarf-2")
SET(CMAKE_C_COMPILER_WORKS TRUE)

SET(CMAKE_CXX_COMPILER "C:/env-windows/tools/bin/../../tools/gnu_gcc/arm_gcc/mingw/bin/arm-none-eabi-g++.exe")
SET(CMAKE_CXX_FLAGS "")
SET(CMAKE_CXX_COMPILER_WORKS TRUE)

SET(CMAKE_OBJCOPY "C:/env-windows/tools/bin/../../tools/gnu_gcc/arm_gcc/mingw/bin/arm-none-eabi-objcopy.exe")
SET(CMAKE_SIZE "C:/env-windows/tools/bin/../../tools/gnu_gcc/arm_gcc/mingw/bin/arm-none-eabi-size.exe")

SET(CMAKE_EXE_LINKER_FLAGS " -mcpu=cortex-m4 -mthumb -mfpu=fpv4-sp-d16 -mfloat-abi=hard -ffunction-sections -fdata-sections -Wl,--gc-sections,-Map=n32g45x-boots.map,-cref,-u,Reset_Handler -T ${CMAKE_SOURCE_DIR}/board/linker_scripts/link.lds -Wl,--gc-sections,--print-memory-usage")

SET(CMAKE_C_STANDARD 11)
SET(CMAKE_CXX_STANDARD 17)

PROJECT(n32g45x-boot C CXX ASM)

INCLUDE_DIRECTORIES(
	.
	applications
	board
	Libraries/N32_Std_Driver/CMSIS/core
	Libraries/N32_Std_Driver/CMSIS/device
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/inc
	Libraries/rt_drivers
)

ADD_DEFINITIONS(
	-DN32G45X
	-D__RTTHREAD__
	-DRT_USING_NEWLIBC
	-DRT_USING_LIBC
	-D_POSIX_C_SOURCE=1
	-DUSE_STDPERIPH_DRIVER
)

# Library source files
AUX_SOURCE_DIRECTORY(applications RT_APPLICATIONS_SOURCES)

SET(RT_DRIVERS_SOURCES
	Libraries/N32_Std_Driver/CMSIS/device/startup/startup_n32g45x_gcc.S
)

SET(RT_LIBRARIES_SOURCES
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_opamp.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_qspi.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_sdio.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_exti.c
	Libraries/N32_Std_Driver/CMSIS/device/system_n32g45x.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_pwr.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_rtc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_can.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_dvp.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_rcc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_crc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_bkp.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_spi.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_usart.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_eth.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_flash.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_wwdg.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_comp.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_dbg.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_tim.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_dac.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_gpio.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_i2c.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_iwdg.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_adc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_tsc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/misc.c
	Libraries/N32_Std_Driver/n32g45x_std_periph_driver/src/n32g45x_dma.c
)

SET(RT_POSIX_SOURCES
)

SET(RT_SMP_SOURCES
)

SET(RT_UTESTCASES_SOURCES
)

# Library search paths
# Library local macro definitions

# Libraries
ADD_LIBRARY(rtt_Drivers OBJECT ${RT_DRIVERS_SOURCES})
ADD_LIBRARY(rtt_Libraries OBJECT ${RT_LIBRARIES_SOURCES})



ADD_EXECUTABLE(${CMAKE_PROJECT_NAME}.elf ${RT_APPLICATIONS_SOURCES})
TARGET_LINK_LIBRARIES(${CMAKE_PROJECT_NAME}.elf
	rtt_Drivers
	rtt_Libraries
	c_nano
)

ADD_CUSTOM_COMMAND(TARGET ${CMAKE_PROJECT_NAME}.elf POST_BUILD 
	COMMAND ${CMAKE_OBJCOPY} -O binary ${CMAKE_PROJECT_NAME}.elf  ${CMAKE_PROJECT_NAME}.bin
	COMMAND ${CMAKE_SIZE} ${CMAKE_PROJECT_NAME}.elf
)

# if custom.cmake is exist, add it
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/custom.cmake)
include(${CMAKE_CURRENT_SOURCE_DIR}/custom.cmake)
endif()
