/**
 * @file    lds_flash.c
 * @brief   Flash操作相关接口实现
 * <AUTHOR> @version v1.0.0
 * @date    2024-02-29
 * @par     Copyright
 * Copyright (c) <PERSON><PERSON><PERSON> Lighting 2018-2025. All rights reserved.
 *
 * @par     History
 * 1.Date         : 2024-02-29
 *   Modification : Create file
 */

/* include files ------------------------------------------------------------ */
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "lds_flash.h"
#include "lds_log.h"

/* macro definition --------------------------------------------------------- */
#define WRITE_MIN_BYTE              4               /**< 写入最小字节数 */
#define FLASH_ADDRESS_MIN           0x08000000      /**< Flash最小地址 */
#define FLASH_ADDRESS_MAX           0x0807FFFF      /**< Flash最大地址 */
#define FLASH_OPERATION_TIMEOUT     0x00002000      /**< Flash操作超时时间 */
/* typedef ------------------------------------------------------------------ */


/* global variables --------------------------------------------------------- */

 
/* local variables ---------------------------------------------------------- */


/* local function declare --------------------------------------------------- */


/* function prototypes ------------------------------------------------------ */

/**
 * @brief 向Flash写入数据
 *
 * @param[in] address 写入起始地址
 * @param[in] buf 写入数据缓冲区
 * @param[in] size 写入数据大小，应该 >= 4 字节
 *
 * @return 0: 成功, 其他: 失败
 * @note 写入前需要先擦除Flash
 */
int ldsFlashWrite(uint32_t address, const uint8_t *buf, uint32_t size)
{
    uint32_t i;
    uint32_t writeData;
    uint32_t readData;
    FLASH_STS flashStatus;

    /* 参数有效性检查 */
    if (buf == NULL) {
        return 1;
    }

    if (size == 0 || (size % WRITE_MIN_BYTE) != 0) {
        return 1;
    }

    if (address < FLASH_ADDRESS_MIN || address > FLASH_ADDRESS_MAX) {
        /* WARN_LOG("write flash address error !\r\n"); */
        return 1;
    }

    /* 检查地址是否4字节对齐 */
    if ((address % WRITE_MIN_BYTE) != 0) {
        return 1;
    }

    /* 检查写入范围是否超出Flash边界 */
    if ((address + size - 1) > FLASH_ADDRESS_MAX) {
        return 1;
    }

    /* FLASH写入 */
    FLASH_ClearFlag(FLASH_FLAG_PGERR | FLASH_FLAG_PVERR | FLASH_FLAG_WRPERR | FLASH_FLAG_EOP | FLASH_FLAG_EVERR);
    FLASH_Unlock();

    for (i = 0; i < size; i += 4, buf += 4, address += 4) {
        memcpy(&writeData, buf, 4); /* 用以保证FLASH_ProgramWord的writeData是内存首地址对齐 */

        flashStatus = FLASH_ProgramWord(address, writeData);
        if (flashStatus != FLASH_COMPL) {
            FLASH_Lock();
            printf("FLASH program error: %d\r\n", flashStatus);
            return 1;
        }

        /* 等待Flash操作完成 */
        flashStatus = FLASH_WaitForLastOpt(FLASH_OPERATION_TIMEOUT);
        if (flashStatus != FLASH_COMPL) {
            FLASH_Lock();
            printf("FLASH operation timeout: %d\r\n", flashStatus);
            return 1;
        }

        readData = *(__IO uint32_t *)(address);

        /* 校验写入数据 */
        if (readData != writeData) {
            FLASH_Lock();
            printf("FLASH data verify error!\r\n");
            return 1;
        }
        /* Flash操作可能非常耗时，如果有看门狗需要喂狗 */
    }
    FLASH_Lock();
    return 0;
}

/**
 * @brief 从Flash读取数据
 *
 * @param[in] address 读取起始地址
 * @param[out] buf 读取数据缓冲区
 * @param[in] size 读取数据大小
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashRead(uint32_t address, uint8_t *buf, uint32_t size)
{
    uint32_t i;

    /* 参数有效性检查 */
    if (buf == NULL) {
        return 1;
    }

    if (size == 0) {
        return 0;
    }

    if (address < FLASH_ADDRESS_MIN || address > FLASH_ADDRESS_MAX) {
        /* WARN_LOG("read flash address error !\r\n"); */
        return 1;
    }

    /* 检查读取范围是否超出Flash边界 */
    if ((address + size - 1) > FLASH_ADDRESS_MAX) {
        return 1;
    }

    for (i = 0; i < size; i++, address++, buf++) {
        *buf = *(__IO uint8_t *)address;
    }

    return 0;
}

/**
 * @brief 擦除Flash指定地址和大小的数据
 *
 * @param[in] address 擦除起始地址
 * @param[in] size 擦除数据大小
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashErase(uint32_t address, uint32_t size)
{
    uint16_t erasePagesNum, i;
    FLASH_STS flashRet;
    uint32_t pageAddress;

    /* 参数有效性检查 */
    if (size == 0) {
        return 0;
    }

    if (address < FLASH_ADDRESS_MIN || address > FLASH_ADDRESS_MAX) {
        /* WARN_LOG("flash address error !\r\n"); */
        return 1;
    }

    if (address % FLASH_PAGE_SIZE) {
        /* WARN_LOG("flash address not page start !\r\n"); */
        return 1;
    }

    /* 检查擦除范围是否超出Flash边界 */
    if ((address + size - 1) > FLASH_ADDRESS_MAX) {
        return 1;
    }

    /* 计算要擦除的页数量 */
    erasePagesNum = size / FLASH_PAGE_SIZE;
    if (size % FLASH_PAGE_SIZE != 0) {
        erasePagesNum++;
    }

    FLASH_ClearFlag(FLASH_FLAG_PGERR | FLASH_FLAG_PVERR | FLASH_FLAG_WRPERR | FLASH_FLAG_EOP | FLASH_FLAG_EVERR);
    FLASH_Unlock();

    /* 一次擦除一个页 */
    for (i = 0; i < erasePagesNum; i++) {
        pageAddress = address + (FLASH_PAGE_SIZE * i);

        /* 检查页地址是否超出范围 */
        if (pageAddress > FLASH_ADDRESS_MAX) {
            break;
        }

        flashRet = FLASH_EraseOnePage(pageAddress);
        if (flashRet != FLASH_COMPL) {
            FLASH_Lock();
            printf("FLASH erase error%d \r\n", flashRet);
            return 1;
        }

        /* 等待Flash操作完成 */
        flashRet = FLASH_WaitForLastOpt(FLASH_OPERATION_TIMEOUT);
        if (flashRet != FLASH_COMPL) {
            FLASH_Lock();
            printf("FLASH erase timeout: %d\r\n", flashRet);
            return 1;
        }
    }

    FLASH_Lock();

    return 0;
}

/**
 * @brief Flash初始化
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashInit(void)
{
    /* 设置Flash访问延迟，根据系统时钟频率设置 */
    FLASH_SetLatency(FLASH_LATENCY_4);

    /* 清除所有Flash状态标志 */
    FLASH_ClearFlag(FLASH_FLAG_PGERR | FLASH_FLAG_PVERR | FLASH_FLAG_WRPERR | FLASH_FLAG_EOP | FLASH_FLAG_EVERR);

    return 0;
}

/**
 * @brief 读取1个字(32位)长度的内容
 *
 * @param[in] addr 起始地址
 *
 * @return 读取的32位数据
 */
static uint32_t ldsFlashReadWord(uint32_t addr)
{
    /* 注意：调用者应确保地址有效性 */
    return *(__IO uint32_t *)addr;
}

/**
 * @brief 读取1个字节长度的内容
 *
 * @param[in] addr 起始地址
 *
 * @return 读取的8位数据
 */
static uint8_t ldsFlashReadByte(uint32_t addr)
{
    /* 注意：调用者应确保地址有效性 */
    return *(__IO uint8_t *)addr;
}

/**
 * @brief 读取多个字(32位)数据
 *
 * @param[in] readAddr 读取起始地址
 * @param[out] pBuffer 读取数据缓冲区
 * @param[in] numToRead 读取字数量
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashReadNumWord(uint32_t readAddr, uint32_t *pBuffer, uint16_t numToRead)
{
    uint16_t i;

    /* 参数有效性检查 */
    if (pBuffer == NULL) {
        return 1;
    }

    if (numToRead == 0) {
        return 0;
    }

    /* 检查地址是否4字节对齐 */
    if ((readAddr % 4) != 0) {
        return 1;
    }

    /* 检查读取范围是否超出Flash边界 */
    if (readAddr < FLASH_ADDRESS_MIN || readAddr > FLASH_ADDRESS_MAX) {
        return 1;
    }

    if ((readAddr + (numToRead * 4) - 1) > FLASH_ADDRESS_MAX) {
        return 1;
    }

    for (i = 0; i < numToRead; i++) {
        pBuffer[i] = ldsFlashReadWord(readAddr);
        readAddr += 4;
    }

    return 0;
}

/**
 * @brief 读取多个字节数据
 *
 * @param[in] readAddr 读取起始地址
 * @param[out] pBuffer 读取数据缓冲区
 * @param[in] numToRead 读取字节数量
 *
 * @return 0: 成功, 其他: 失败
 */
int ldsFlashReadNumByte(uint32_t readAddr, uint8_t *pBuffer, uint16_t numToRead)
{
    uint16_t i;
    uint16_t numWord;
    uint16_t remainBytes;
    uint32_t readWord;

    /* 参数有效性检查 */
    if (pBuffer == NULL) {
        return 1;
    }

    if (numToRead == 0) {
        return 0;
    }

    /* 检查读取范围是否超出Flash边界 */
    if (readAddr < FLASH_ADDRESS_MIN || readAddr > FLASH_ADDRESS_MAX) {
        return 1;
    }

    if ((readAddr + numToRead - 1) > FLASH_ADDRESS_MAX) {
        return 1;
    }

    numWord = numToRead / 4;
    remainBytes = numToRead % 4;

    /* 读取完整的字 */
    for (i = 0; i < numWord; i++) {
        readWord = ldsFlashReadWord(readAddr);

        pBuffer[4 * i] = (uint8_t)(readWord);
        pBuffer[4 * i + 1] = (uint8_t)(readWord >> 8);
        pBuffer[4 * i + 2] = (uint8_t)(readWord >> 16);
        pBuffer[4 * i + 3] = (uint8_t)(readWord >> 24);

        readAddr += 4;
    }

    /* 读取剩余的字节 */
    if (remainBytes > 0) {
        readWord = ldsFlashReadWord(readAddr);
        for (i = 0; i < remainBytes; i++) {
            pBuffer[numWord * 4 + i] = (uint8_t)(readWord >> (i * 8));
        }
    }

    return 0;
}
/*----------------------------------------------------------------------------*/

/**************************** END OF FILE *************************************/
