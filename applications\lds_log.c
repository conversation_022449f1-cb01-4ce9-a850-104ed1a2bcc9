/**
 * @file    lds_log.c
 * @brief
 * <AUTHOR> @version v1.0.0
 * @date
 * 
 * @copyright Copyright (c) <PERSON><PERSON><PERSON> ~. All rights reserved.
 */
 
/* include files ------------------------------------------------------------ */
#include "lds_log.h"
#include <stdlib.h>
#include <stdarg.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <ctype.h>
/* macro definition --------------------------------------------------------- */


/* typedef ------------------------------------------------------------------ */


/* global variables --------------------------------------------------------- */


/* local variables ---------------------------------------------------------- */


/* local function declare --------------------------------------------------- */


/* function prototypes ------------------------------------------------------ */
#undef errno
extern int errno;
extern int _end;

caddr_t _sbrk(int incr)
{
    static unsigned char *heap = NULL;
    unsigned char *prev_heap;

    if (heap == NULL)
    {
        heap = (unsigned char *)&_end;
    }
    prev_heap = heap;

    heap += incr;

    return (caddr_t)prev_heap;
}

int link(char *old, char *new)
{
    return -1;
}

int _close(int file)
{
    return -1;
}

int _fstat(int file, struct stat *st)
{
    st->st_mode = S_IFCHR;
    return 0;
}

int _isatty(int file)
{
    return 1;
}

int _lseek(int file, int ptr, int dir)
{
    return 0;
}

void abort(void)
{
    /* Abort called */
    while (1)
        ;
}

int _write(int fd, char *pBuffer, int size)
{
    uint32_t i;

    for (i = 0; i < size; i++)
    {
        fputc((uint8_t)pBuffer[i], stdout);
    }
    return size;
}

int _read(int fd, char *pBuffer, int size)
{
    return size;
}

int ldsUartLogInit(void)
{
    GPIO_InitType GPIO_InitStructure;
    USART_InitType USART_InitStructure;

    // close JTAG

    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO | RCC_APB2_PERIPH_GPIOA, ENABLE);

    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_USART1, ENABLE);


    GPIO_InitStructure.Pin        = GPIO_PIN_9;
    GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitStructure);

    GPIO_InitStructure.Pin       = GPIO_PIN_10;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitStructure);

    USART_InitStructure.BaudRate            = 115200;
    USART_InitStructure.WordLength          = USART_WL_8B;
    USART_InitStructure.StopBits            = USART_STPB_1;
    USART_InitStructure.Parity              = USART_PE_NO;
    USART_InitStructure.HardwareFlowControl = USART_HFCTRL_NONE;
    USART_InitStructure.Mode                = USART_MODE_RX | USART_MODE_TX;

    // init uart
    USART_Init(USART1, &USART_InitStructure);

    // enable uart
    USART_Enable(USART1, ENABLE);

    USART_ClrFlag(USART1, USART_FLAG_TXDE | USART_FLAG_TXC);

    return 0;
}

int ldsUsartLogSendByte(uint8_t ucData)
{
    while(USART_GetFlagStatus(USART1, USART_FLAG_TXDE) == RESET) {
        __NOP();
    }
    //发送
    USART_SendData(USART1, ucData);
    return 0;
}

int ldsUsartLogSendBuffer(uint8_t *pucData, uint32_t uiLen)
{
    if (pucData == NULL)
        return -1;

    for (uint32_t uiIndex = 0; uiIndex < uiLen; uiIndex++) {
        ldsUsartLogSendByte(pucData[uiIndex]);
    }

    return 0;
}

int ldsUsartLogSendString(char *pStr)
{
    if (pStr == NULL)
        return -1;

    for (uint32_t uiIndex = 0; uiIndex < strlen(pStr); uiIndex++) {
        ldsUsartLogSendByte(pStr[uiIndex]);
    }

    return 0;
}

int ldsUsartLogOutput(char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    vprintf(fmt, args);
    va_end(args);

    return 0;
}

int fputc(int ch, FILE *f)
{
    ldsUsartLogSendByte(ch);
    return (ch);
}
/**************************** END OF FILE *************************************/
