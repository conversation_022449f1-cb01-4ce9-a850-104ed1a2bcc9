/**
 * @file lds_ymodem.c
 * @brief YModem协议升级模块实现
 * <AUTHOR>
 * @date 2024
 */

#include "main.h"
#include "lds_ymodem.h"
#include "lds_log.h"
#include "lds_flash.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <n32g45x.h>

static uint8_t g_ymodem_buffer[YMODEM_PACKET_SIZE_1024 + YMODEM_PACKET_HEADER + YMODEM_PACKET_CRC];
static uint32_t g_current_addr = 0;
static uint32_t g_received_size = 0;

/**
 * @brief 串口发送单个字节
 */
static void ldsYmodemSendByte(uint8_t byte)
{
    // 等待发送缓冲区空
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXDE) == RESET);
    USART_SendData(USART1, byte);
}

/**
 * @brief 串口接收单个字节（带超时）
 */
static bool ldsYmodemReceiveByte(uint8_t* byte, uint32_t timeout_ms)
{
    uint32_t start_time = 0; // 简化实现，实际需要系统时钟
    uint32_t count = 0;
    
    while (count < timeout_ms * 1000) { // 简单计数延时
        if (USART_GetFlagStatus(USART1, USART_FLAG_RXDNE) != RESET) {
            *byte = USART_ReceiveData(USART1);
            return true;
        }
        count++;
    }
    return false;
}

/**
 * @brief 计算CRC16
 */
static uint16_t ldsYmodemCalcCrc16(const uint8_t* data, uint16_t length)
{
    uint16_t crc = 0;
    
    while (length--) {
        crc ^= (uint16_t)(*data++) << 8;
        for (uint8_t i = 0; i < 8; i++) {
            if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}

/**
 * @brief 接收YModem数据包
 */
static lds_ymodem_result_t ldsYmodemReceivePacket(uint8_t* packet, uint16_t* packet_size)
{
    uint8_t header;
    uint8_t packet_num, packet_num_inv;
    uint16_t crc_received, crc_calculated;
    uint16_t data_size;
    
    // 接收包头
    if (!ldsYmodemReceiveByte(&header, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }
    
    if (header == YMODEM_EOT) {
        *packet_size = 0;
        return YMODEM_OK;
    }
    
    if (header == YMODEM_CAN) {
        return YMODEM_CANCEL;
    }
    
    if (header != YMODEM_SOH && header != YMODEM_STX) {
        return YMODEM_ERROR;
    }
    
    data_size = (header == YMODEM_SOH) ? YMODEM_PACKET_SIZE_128 : YMODEM_PACKET_SIZE_1024;
    
    // 接收包序号和反码
    if (!ldsYmodemReceiveByte(&packet_num, YMODEM_TIMEOUT_MS) ||
        !ldsYmodemReceiveByte(&packet_num_inv, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }
    
    if ((packet_num + packet_num_inv) != 0xFF) {
        return YMODEM_ERROR;
    }
    
    // 接收数据
    for (uint16_t i = 0; i < data_size; i++) {
        if (!ldsYmodemReceiveByte(&packet[i], YMODEM_TIMEOUT_MS)) {
            return YMODEM_TIMEOUT;
        }
    }
    
    // 接收CRC
    uint8_t crc_high, crc_low;
    if (!ldsYmodemReceiveByte(&crc_high, YMODEM_TIMEOUT_MS) ||
        !ldsYmodemReceiveByte(&crc_low, YMODEM_TIMEOUT_MS)) {
        return YMODEM_TIMEOUT;
    }
    
    crc_received = (crc_high << 8) | crc_low;
    crc_calculated = ldsYmodemCalcCrc16(packet, data_size);
    
    if (crc_received != crc_calculated) {
        return YMODEM_CRC_ERROR;
    }
    
    *packet_size = data_size;
    return YMODEM_OK;
}

bool ldsYmodemParseFilename(const char* filename, lds_ymodem_file_info_t* file_info)
{
    // 文件名格式: host-V1.00.02-23abcdef.bin
    // 解析版本号和CRC32值

    if (!filename || !file_info) {
        return false;
    }

    strncpy(file_info->filename, filename, sizeof(file_info->filename) - 1);
    file_info->filename[sizeof(file_info->filename) - 1] = '\0';

    // 查找版本号 (V后面的部分)
    const char* version_start = strstr(filename, "-V");
    if (!version_start) {
        return false;
    }
    version_start += 2; // 跳过 "-V"

    // 查找CRC32值 (最后一个-后面的8位十六进制)
    const char* crc_start = strrchr(filename, '-');
    if (!crc_start || strlen(crc_start) < 9) { // "-" + 8位十六进制
        return false;
    }
    crc_start += 1; // 跳过 "-"

    // 提取版本号
    size_t version_len = crc_start - version_start - 1; // -1 for the '-' before CRC
    if (version_len >= sizeof(file_info->version)) {
        version_len = sizeof(file_info->version) - 1;
    }
    strncpy(file_info->version, version_start, version_len);
    file_info->version[version_len] = '\0';

    // 提取CRC32值
    char crc_str[9];
    strncpy(crc_str, crc_start, 8);
    crc_str[8] = '\0';

    // 检查是否都是十六进制字符
    for (int i = 0; i < 8; i++) {
        if (!((crc_str[i] >= '0' && crc_str[i] <= '9') ||
              (crc_str[i] >= 'a' && crc_str[i] <= 'f') ||
              (crc_str[i] >= 'A' && crc_str[i] <= 'F'))) {
            return false;
        }
    }

    file_info->expected_crc32 = strtoul(crc_str, NULL, 16);

    return true;
}

void ldsYmodemInit(void)
{
    // YModem升级模块初始化，无需打印信息
}

lds_ymodem_result_t ldsYmodemUpgrade(void)
{
    lds_ymodem_file_info_t file_info = {0};
    uint8_t packet_num = 0;
    uint16_t packet_size;
    lds_ymodem_result_t result;
    uint8_t retry_count = 0;
    bool first_packet = true;
    bool file_received = false;

    // 发送'C'开始传输
    ldsYmodemSendByte(YMODEM_C);

    while (1) {
        result = ldsYmodemReceivePacket(g_ymodem_buffer, &packet_size);

        if (result == YMODEM_TIMEOUT) {
            retry_count++;
            if (retry_count >= YMODEM_MAX_RETRY) {
                // 先发送回复，再打印错误信息
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("YModem timeout, max retries reached\n");
                return YMODEM_TIMEOUT;
            }
            ldsYmodemSendByte(YMODEM_C);
            continue;
        }

        if (result == YMODEM_CANCEL) {
            // 传输被取消，直接打印错误信息
            printf("YModem transfer cancelled\n");
            return YMODEM_CANCEL;
        }

        if (result != YMODEM_OK) {
            ldsYmodemSendByte(YMODEM_NAK);
            retry_count++;
            if (retry_count >= YMODEM_MAX_RETRY) {
                // 先发送回复，再打印错误信息
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("YModem error, max retries reached\n");
                return result;
            }
            continue;
        }

        retry_count = 0;

        // 处理EOT
        if (packet_size == 0) {
            ldsYmodemSendByte(YMODEM_ACK);
            if (file_received) {
                // 第二次EOT，传输会话结束
                break;
            } else {
                // 第一次EOT，文件传输结束，等待下一个文件或会话结束
                ldsYmodemSendByte(YMODEM_C);
                first_packet = true;
                file_received = true;
                continue;
            }
        }

        // 处理第一个包（文件信息包）
        if (first_packet) {
            first_packet = false;

            if (g_ymodem_buffer[0] == 0) {
                // 空文件名，传输结束
                ldsYmodemSendByte(YMODEM_ACK);
                break;
            }

            // 解析文件名和大小
            char* filename = (char*)g_ymodem_buffer;
            char* filesize_str = filename + strlen(filename) + 1;

            if (!ldsYmodemParseFilename(filename, &file_info)) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Invalid filename format %s\n", file_info.filename);
                return YMODEM_ERROR;
            }

            file_info.filesize = strtoul(filesize_str, NULL, 10);

            if (file_info.filesize > APP_MAX_SIZE) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("File too large: %u bytes\n", file_info.filesize);
                return YMODEM_FILE_TOO_LARGE;
            }

            // 选择写入地址（APP1区域）
            g_current_addr = APP1_START_ADDR;
            g_received_size = 0;

            // 擦除APP1区域
            if (ldsFlashErase(g_current_addr, file_info.filesize) != 0) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Flash erase failed\n");
                return YMODEM_ERROR;
            }

            ldsYmodemSendByte(YMODEM_ACK);
            ldsYmodemSendByte(YMODEM_C);
            packet_num = 1;
            continue;
        }

        // 处理数据包
        uint32_t write_size = packet_size;
        if (g_received_size + write_size > file_info.filesize) {
            write_size = file_info.filesize - g_received_size;
        }

        if (write_size > 0) {
            if (ldsFlashWrite(g_current_addr, g_ymodem_buffer, write_size) != 0) {
                ldsYmodemSendByte(YMODEM_CAN);
                delay_ms(10);
                printf("Flash write failed at 0x%08X\n", g_current_addr);
                return YMODEM_ERROR;
            }

            g_current_addr += write_size;
            g_received_size += write_size;
        }

        ldsYmodemSendByte(YMODEM_ACK);
        packet_num++;
    }
    
    // 校验接收到的文件
    if (g_received_size > 0) {
        uint32_t calculated_crc = ldsUtilCheckCrc32((const uint8_t*)APP1_START_ADDR, g_received_size);

        if (calculated_crc == file_info.expected_crc32) {
            // 更新BootInfo
            BootInfo_t new_boot_info = {0};

            new_boot_info.magic = BOOT_INFO_MAGIC;
            new_boot_info.boot_target = APP_TARGET_APP1;
            new_boot_info.app1_crc32 = file_info.expected_crc32;
            new_boot_info.app1_size = g_received_size;
            strncpy(new_boot_info.app1_version, file_info.version, sizeof(new_boot_info.app1_version) - 1);
            new_boot_info.app1_version[sizeof(new_boot_info.app1_version) - 1] = 0;

            // 计算并更新boot info的CRC32
            new_boot_info.crc32 = ldsUtilCheckCrc32((const uint8_t*)&new_boot_info, (sizeof(BootInfo_t) - 4));
            printf("New boot info: target: %s, app1_crc32: 0x%08X, app1_size: %u, app1_version: %s, crc32: 0x%08X\n",
                   (new_boot_info.boot_target == APP_TARGET_APP1) ? "APP1" : "APP2",
                   new_boot_info.app1_crc32, new_boot_info.app1_size, new_boot_info.app1_version, new_boot_info.crc32);
            // 写入新的boot info
            if (ldsFlashErase(BOOT_INFO_ADDR, sizeof(BootInfo_t)) == 0 &&
                ldsFlashWrite(BOOT_INFO_ADDR, (uint8_t*)&new_boot_info, sizeof(BootInfo_t)) == 0) {
                return YMODEM_OK;
            } else {
                printf("Failed to update boot info\n");
                return YMODEM_ERROR;
            }
        } else {
            printf("File CRC32 verification failed: expected 0x%08X, got 0x%08X\n",
                   file_info.expected_crc32, calculated_crc);
            return YMODEM_CRC_ERROR;
        }
    }
    
    return YMODEM_OK;
}